{"expo": {"name": "BLEExpoBlog", "slug": "BLEExpoBlog", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.ibrahimyilmaz.bleexpoblog", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_CONNECT"], "package": "com.ibrahimyilmaz.bleexpoblog"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["react-native-ble-plx", {"isBackgroundEnabled": true, "modes": ["peripheral", "central"], "bluetoothAlwaysPermission": "Allow $(PRODUCT_NAME) to connect to bluetooth devices"}]], "extra": {"eas": {"projectId": "68f09908-a5a1-44c4-964b-463e34c629d3"}}}}