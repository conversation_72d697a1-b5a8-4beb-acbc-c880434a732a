{"name": "bleexpoblog", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.0", "expo-device": "~7.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-base64": "^0.2.1", "react-native-ble-plx": "^3.2.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.10", "@types/react-native-base64": "^0.2.2", "typescript": "~5.8.3"}, "private": true}