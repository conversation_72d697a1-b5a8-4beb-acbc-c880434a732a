{"name": "bleexpoblog", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~51.0.26", "expo-device": "~6.0.2", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-base64": "^0.2.1", "react-native-ble-plx": "^3.2.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native-base64": "^0.2.2", "typescript": "^5.1.3"}, "private": true}